import { logCustomEvent } from '@analytics/handlers/airship';
import { PushNotificationConsentService } from '@analytics/handlers/PushNotificationConsentService';
import { SFUserConsent } from '@bp/pulse-auth-sdk';
import { PermissionStatus } from '@ua/react-native-airship';

const handleNotificationPermissions = async (
  isSalesForceAccepted: boolean,
  permissionStatus: PermissionStatus,
): Promise<void> => {
  let permissionSuffix;

  if (permissionStatus === PermissionStatus.Granted) {
    permissionSuffix = 'true';
  } else if (permissionStatus === PermissionStatus.Denied) {
    permissionSuffix = 'false';
  } else {
    permissionSuffix = 'not_determined';
  }
  if (isSalesForceAccepted) {
    await logCustomEvent(`customer_sf_consent_true_native_${permissionSuffix}`);
  } else {
    await logCustomEvent(
      `customer_sf_consent_false_native_${permissionSuffix}`,
    );
  }
};

export const checkUserNotificationConsent = async (
  authenticated: boolean | null,
  getConsents: () => Promise<SFUserConsent[] | undefined>,
): Promise<void> => {
  if (!authenticated) {
    return;
  }

  const pnConsentService = new PushNotificationConsentService();
  const salesForceConsents = await getConsents();
  const isSalesForceAccepted =
    await pnConsentService.isSalesForcePushConsentAccepted(salesForceConsents);

  // Handle native permission status
  const notificationStatus = await pnConsentService.nativePermissionStatus();
  await handleNotificationPermissions(
    isSalesForceAccepted,
    notificationStatus.notificationPermissionStatus,
  );

  // Enable Airship notifications if both Salesforce consent and native permissions are granted
  // if (
  //   isSalesForceAccepted &&
  //   notificationStatus.notificationPermissionStatus === PermissionStatus.Granted
  // ) {
  //   try {
  //     await pnConsentService.enableAirshipNotifications();
  //     await logCustomEvent('airship_notifications_enabled_after_consent_check');
  //   } catch (err) {
  //     // Preserve app stability & surface diagnostics
  //     await logCustomEvent(
  //       'airship_notifications_enable_failed_after_consent_check',
  //     );
  //     console.warn(
  //       '[pnConsentHelper] Failed to enable Airship notifications',
  //       err,
  //     );
  //   }
  // }
};
